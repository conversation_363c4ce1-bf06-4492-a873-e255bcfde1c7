import { useAuth } from "@/hooks/useAuth";
import { useLocations } from "@/hooks/useLocations";
import { useOrganization } from "@/hooks/useOrganization";
import { ALL_LOCATIONS_ID } from "@/types/location";
import { useEffect, useRef } from "react";
import { useSearchParams } from "react-router-dom";

/**
 * Hook to restore organization and location filters from URL parameters on page refresh
 * This ensures that when users refresh the page, their selected filters are maintained
 */
export function useUrlFilterRestore() {
  const [searchParams] = useSearchParams();
  const { organization, setOrganization } = useAuth();
  const { availableOrgs, restoreFromUrl: restoreOrgFromUrl, isLoading: orgLoading } = useOrganization();
  const { locations, selectedLocation, handleLocationSelect, isLoading: locationLoading } = useLocations();
  const hasRestoredRef = useRef(false);

  useEffect(() => {
    // Only run once and only after organizations are loaded
    if (hasRestoredRef.current || orgLoading || !availableOrgs.length) {
      return;
    }

    const orgParam = searchParams.get("org");

    // Restore organization from URL
    if (orgParam && !organization) {
      let targetOrgId = orgParam;

      // Handle "all" parameter for system admin all orgs view
      if (orgParam === "all") {
        targetOrgId = "system-admin-all-orgs";
      }

      // Find the organization in available orgs
      const targetOrg = availableOrgs.find(org => org.id === targetOrgId);
      if (targetOrg) {
        console.log('[useUrlFilterRestore] Restoring organization from URL:', targetOrg.name);
        // Restore organization without updating URL to avoid loops
        restoreOrgFromUrl(targetOrgId);
        setOrganization(targetOrg);
      }
    }

    // Mark as restored to prevent running again
    hasRestoredRef.current = true;
  }, [orgLoading, availableOrgs, organization, searchParams, restoreOrgFromUrl, setOrganization]);

  // Restore location from URL (runs after organization is restored and locations are loaded)
  useEffect(() => {
    // Only run if we have an organization, locations are loaded, and we haven't restored location yet
    if (!organization || locationLoading || !locations.length || selectedLocation) {
      return;
    }

    const locationParam = searchParams.get("location");
    if (locationParam) {
      let targetLocationId = locationParam;

      // Handle "all" parameter for all locations view
      if (locationParam === "all") {
        targetLocationId = ALL_LOCATIONS_ID;
      }

      // Find the location in available locations or handle "all locations"
      let targetLocation = null;
      if (targetLocationId === ALL_LOCATIONS_ID) {
        // Create the "All Locations" virtual location
        targetLocation = {
          id: ALL_LOCATIONS_ID,
          organization_id: organization.id,
          name: "All Locations",
          type: "system" as const,
          address: {},
          contact_info: {},
          operating_hours: null,
          settings: {},
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        };
      } else {
        targetLocation = locations.find(loc => loc.id === targetLocationId);
      }

      if (targetLocation) {
        console.log('[useUrlFilterRestore] Restoring location from URL:', targetLocation.name);
        // Restore location without updating URL to avoid loops
        handleLocationSelect(targetLocation, false);
      }
    }
  }, [organization, locationLoading, locations, selectedLocation, searchParams, handleLocationSelect]);

  return {
    isRestoring: orgLoading || locationLoading,
    hasRestored: hasRestoredRef.current
  };
}
