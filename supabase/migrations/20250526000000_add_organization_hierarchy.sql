-- Add parent-child hierarchy to organizations
-- This migration adds support for organizational hierarchies while maintaining
-- the ability for organizations at any level to have locations

BEGIN;

-- Add parent_id column to organizations table
ALTER TABLE public.organizations
ADD COLUMN parent_id uuid REFERENCES public.organizations(id) ON DELETE SET NULL;

-- Add hierarchy level for easier querying and validation
ALTER TABLE public.organizations
ADD COLUMN hierarchy_level integer DEFAULT 0 CHECK (hierarchy_level >= 0 AND hierarchy_level <= 10);

-- Add path for efficient hierarchy queries (materialized path pattern)
ALTER TABLE public.organizations
ADD COLUMN hierarchy_path text;

-- Create index for parent_id lookups
CREATE INDEX idx_organizations_parent_id ON public.organizations(parent_id);

-- Create index for hierarchy path queries
CREATE INDEX idx_organizations_hierarchy_path ON public.organizations(hierarchy_path);

-- Create index for level-based queries
CREATE INDEX idx_organizations_hierarchy_level ON public.organizations(hierarchy_level);

-- Function to update hierarchy path and level
CREATE OR REPLACE FUNCTION update_organization_hierarchy()
RETURNS TRIGGER AS $$
DECLARE
    parent_path text;
    parent_level integer;
BEGIN
    -- If this is a root organization (no parent)
    IF NEW.parent_id IS NULL THEN
        NEW.hierarchy_level := 0;
        NEW.hierarchy_path := NEW.id::text;
    ELSE
        -- Get parent's path and level
        SELECT hierarchy_path, hierarchy_level
        INTO parent_path, parent_level
        FROM public.organizations
        WHERE id = NEW.parent_id;

        -- Prevent circular references
        IF parent_path LIKE '%' || NEW.id::text || '%' THEN
            RAISE EXCEPTION 'Circular reference detected in organization hierarchy';
        END IF;

        -- Set new level and path
        NEW.hierarchy_level := parent_level + 1;
        NEW.hierarchy_path := parent_path || '.' || NEW.id::text;

        -- Prevent too deep hierarchies
        IF NEW.hierarchy_level > 10 THEN
            RAISE EXCEPTION 'Organization hierarchy cannot exceed 10 levels';
        END IF;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update hierarchy
CREATE TRIGGER trigger_update_organization_hierarchy
    BEFORE INSERT OR UPDATE OF parent_id ON public.organizations
    FOR EACH ROW
    EXECUTE FUNCTION update_organization_hierarchy();

-- Function to get all descendants of an organization
CREATE OR REPLACE FUNCTION get_organization_descendants(org_id uuid)
RETURNS TABLE(
    id uuid,
    name text,
    type text,
    hierarchy_level integer,
    hierarchy_path text
) AS $$
DECLARE
    parent_path text;
BEGIN
    -- Get the parent organization's path
    SELECT o.hierarchy_path INTO parent_path
    FROM public.organizations o
    WHERE o.id = org_id;

    IF parent_path IS NULL THEN
        RETURN;
    END IF;

    RETURN QUERY
    SELECT o.id, o.name, o.type, o.hierarchy_level, o.hierarchy_path
    FROM public.organizations o
    WHERE o.hierarchy_path LIKE parent_path || '.%'
    ORDER BY o.hierarchy_level, o.name;
END;
$$ LANGUAGE plpgsql;

-- Function to get all ancestors of an organization
CREATE OR REPLACE FUNCTION get_organization_ancestors(org_id uuid)
RETURNS TABLE(
    id uuid,
    name text,
    type text,
    hierarchy_level integer,
    hierarchy_path text
) AS $$
DECLARE
    org_path text;
    path_parts text[];
    part_id uuid;
BEGIN
    -- Get the organization's path
    SELECT o.hierarchy_path INTO org_path
    FROM public.organizations o
    WHERE o.id = org_id;

    IF org_path IS NULL THEN
        RETURN;
    END IF;

    -- Split the path and return each ancestor
    path_parts := string_to_array(org_path, '.');

    FOR i IN 1..array_length(path_parts, 1) - 1 LOOP
        part_id := path_parts[i]::uuid;
        RETURN QUERY
        SELECT o.id, o.name, o.type, o.hierarchy_level, o.hierarchy_path
        FROM public.organizations o
        WHERE o.id = part_id;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Function to get organization tree (with children)
CREATE OR REPLACE FUNCTION get_organization_tree(root_org_id uuid DEFAULT NULL)
RETURNS TABLE(
    id uuid,
    name text,
    type text,
    parent_id uuid,
    hierarchy_level integer,
    hierarchy_path text,
    location_count bigint
) AS $$
BEGIN
    RETURN QUERY
    WITH RECURSIVE org_tree AS (
        -- Base case: root organizations or specific root
        SELECT
            o.id,
            o.name,
            o.type,
            o.parent_id,
            o.hierarchy_level,
            o.hierarchy_path,
            COALESCE(l.location_count, 0) as location_count
        FROM public.organizations o
        LEFT JOIN (
            SELECT organization_id, COUNT(*) as location_count
            FROM public.locations
            GROUP BY organization_id
        ) l ON o.id = l.organization_id
        WHERE (root_org_id IS NULL AND o.parent_id IS NULL)
           OR (root_org_id IS NOT NULL AND o.id = root_org_id)

        UNION ALL

        -- Recursive case: children
        SELECT
            child.id,
            child.name,
            child.type,
            child.parent_id,
            child.hierarchy_level,
            child.hierarchy_path,
            COALESCE(l.location_count, 0) as location_count
        FROM public.organizations child
        LEFT JOIN (
            SELECT organization_id, COUNT(*) as location_count
            FROM public.locations
            GROUP BY organization_id
        ) l ON child.id = l.organization_id
        INNER JOIN org_tree parent ON child.parent_id = parent.id
    )
    SELECT * FROM org_tree
    ORDER BY hierarchy_level, name;
END;
$$ LANGUAGE plpgsql;

-- Update existing organizations to have proper hierarchy paths
UPDATE public.organizations
SET hierarchy_path = id::text, hierarchy_level = 0
WHERE parent_id IS NULL;

COMMIT;
